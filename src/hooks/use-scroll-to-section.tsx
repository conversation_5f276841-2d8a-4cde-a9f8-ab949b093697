import { useCallback } from 'react';

export const useScrollToSection = () => {
  const scrollToSection = useCallback((sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (!element) {
      console.warn(`Section with id "${sectionId}" not found`);
      return;
    }

    const elementTop = element.offsetTop;
    const elementHeight = element.offsetHeight;
    const viewportHeight = window.innerHeight;
    const navigationHeight = 80; // Account for fixed navigation
    
    // Calculate scroll position to center the section in viewport
    // If section is taller than viewport, scroll to top of section
    let scrollTo: number;
    
    if (elementHeight > viewportHeight - navigationHeight) {
      // Section is taller than viewport, scroll to top with navigation offset
      scrollTo = elementTop - navigationHeight;
    } else {
      // Center the section in viewport
      scrollTo = elementTop - (viewportHeight / 2) + (elementHeight / 2);
    }
    
    // Ensure we don't scroll past the top of the page
    scrollTo = Math.max(0, scrollTo);
    
    // Ensure we don't scroll past the bottom of the page
    const maxScroll = document.documentElement.scrollHeight - viewportHeight;
    scrollTo = Math.min(scrollTo, maxScroll);
    
    window.scrollTo({
      top: scrollTo,
      behavior: 'smooth'
    });
  }, []);

  const scrollToSectionWithDelay = useCallback((sectionId: string, delay: number = 100) => {
    // Add delay to ensure DOM is ready, especially useful for page navigation
    setTimeout(() => {
      scrollToSection(sectionId);
    }, delay);
  }, [scrollToSection]);

  return { 
    scrollToSection, 
    scrollToSectionWithDelay 
  };
};
