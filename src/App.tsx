
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Work from "./pages/Work";
import CaseStudy from "./pages/CaseStudy";
import CsdCaseStudy from "./pages/CsdCaseStudy";
import GhwinCaseStudy from "./pages/GhwinCaseStudy";
import FlareAICaseStudy from "./pages/FlareAICaseStudy";
import DefyMedicalCaseStudy from "./pages/DefyMedicalCaseStudy";
import NymbusCaseStudy from "./pages/NymbusCaseStudy";
import CareAiCaseStudy from "./pages/CareAiCaseStudy";
import Contact from "./pages/Contact";
import FoundersCohort from "./pages/FoundersCohort";
import CohortApplication from "./pages/CohortApplication";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/work" element={<Work />} />
          <Route path="/founders-cohort" element={<FoundersCohort />} />
          <Route path="/founders-cohort/case-study/nymbus" element={<NymbusCaseStudy />} />
          <Route path="/founders-cohort/case-study/care-ai" element={<CareAiCaseStudy />} />
          <Route path="/cohort-application" element={<CohortApplication />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/case-study/:slug" element={<CaseStudy />} />
          <Route path="/case-study/csd-redesign" element={<CsdCaseStudy />} />
          <Route path="/case-study/ghwin-case-study" element={<GhwinCaseStudy />} />
          <Route path="/case-study/flare-ai" element={<FlareAICaseStudy />} />
          <Route path="/case-study/defy-medical" element={<DefyMedicalCaseStudy />} />
          <Route path="/case-study/nymbus" element={<NymbusCaseStudy />} />
          <Route path="/case-study/care-ai" element={<CareAiCaseStudy />} />
          <Route path="/case-study-ghwin" element={<GhwinCaseStudy />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
