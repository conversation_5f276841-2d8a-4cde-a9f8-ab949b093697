import { useEffect } from "react";
import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";
import OptimizedImage from "@/components/ui/optimized-image";
import { useImagePreload } from "@/hooks/use-image-preload";

const FlareAICaseStudy = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    // Only scroll to top if there's no hash in the URL
    if (!window.location.hash) {
      window.scrollTo(0, 0);
    }
  }, []);
  
  // Preload critical images for better performance
  useImagePreload([
    "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=1920&h=1080&fit=crop",
    "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800&h=600&fit=crop",
    "https://images.unsplash.com/photo-1620712943543-bcc4688e7485?w=800&h=600&fit=crop"
  ], { priority: true });
  
  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      {/* Hero Section with Large Image */}
      <div className="pt-48 pb-0">
        <div className="max-w-7xl mx-auto px-6">
          {/* Project Title */}
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-7xl font-semibold text-gray-900 mb-8 leading-[0.9] tracking-tight">
              When AI Meets Compliance: Shaping Flare's Identity for the Future of Finance
            </h1>
            <div className="flex justify-center gap-8 text-sm text-gray-500 uppercase tracking-wider">
              <span>UI/UX Design</span>
              <span>•</span>
              <span>Website Design</span>
              <span>•</span>
              <span>Brand Identity</span>
              <span>•</span>
              <span>2025</span>
            </div>
          </div>
        </div>
        
        {/* Full Width Hero Image */}
        <div className="w-full">
          <OptimizedImage 
            src="/lovable-uploads/cdd72de5-f508-4527-bf93-ef02a1fb10ce.webp"
            alt="FlareAI logo and branding"
            className="w-full h-[70vh]"
            priority={true}
            sizes="100vw"
          />
        </div>
      </div>

      {/* Project Overview */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-16">
            <div>
              <h2 className="text-3xl font-semibold text-gray-900 mb-8">Project Overview</h2>
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                Flare is an enterprise-grade AI platform built specifically for banks and financial institutions. With a mission to make AI adoption seamless, secure, and compliant, Flare needed a brand that would convey trust, intelligence, and innovation at first glance.
              </p>
              <p className="text-gray-600 leading-relaxed mb-8">
                I was brought in to create the brand from the ground up — developing a visual identity that felt bold yet credible, and modern without losing the gravitas required in financial services. From there, I designed a website that clearly communicated Flare's value proposition to bank executives and compliance teams alike, balancing clarity with sophistication. I also led the UI design for the Flare platform itself, ensuring the product experience matched the promise of the brand: powerful, secure, and easy to use.
              </p>
              <p className="text-gray-600 leading-relaxed">
                The result is a cohesive system — brand, site, and product — that positions Flare as a trusted partner in the future of financial AI.
              </p>
            </div>
            <div className="space-y-8">
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Client</h3>
                <p className="text-gray-600">Flare AI</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Timeline</h3>
                <p className="text-gray-600">6 months</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900 uppercase tracking-wider mb-2">Services</h3>
                <p className="text-gray-600">UI/UX Design, Web Design, Brand Identity, Product Strategy</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* FlareAI Interface Section */}
      <div className="py-24">
        <div className="max-w-7xl mx-auto px-6">
          <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
            <OptimizedImage 
              src="/lovable-uploads/b5420843-f317-4a16-8b52-a05a92e32182.webp"
              alt="Flare AI interface"
              className="w-full h-auto"
              priority={true}
              sizes="100vw"
            />
          </div>
      </div>

      {/* Brand Story Section */}
      <div className="py-16">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">THE BRAND STORY</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              To help Flare stand apart in a crowded AI space, I developed a distinct set of branded AI agents — each with their own name, personality, and function. From Beacon (Social Media) to Ember (Ad Manager), these characters serve as a visual and conceptual bridge between Flare's cutting-edge technology and the real-world business problems it solves.
            </p>
            <p>
              Each agent is designed to represent a core pillar of digital growth: social engagement, sales automation, advertising, web conversion, marketing strategy, and executive assistance. By giving each one a unique flame-inspired name and color-coded identity, the platform becomes more intuitive, memorable, and emotionally engaging — especially for customers navigating complex AI tools.
            </p>
            <p>
              The result is more than just a clean UI — it's a brand story where every interaction reinforces clarity, capability, and cohesion. These agents bring warmth and personality to a traditionally cold tech category, while reinforcing Flare's positioning as a modern AI partner built to empower real businesses.
            </p>
          </div>
        </div>
      </div>
      </div>

      {/* Website Design Section */}
      <div className="py-24">
        <div className="max-w-4xl mx-auto px-6">
          <div className="relative">
            {/* Desktop Website */}
            <OptimizedImage 
              src="/lovable-uploads/f94394c7-4bf9-4969-89bf-1e5ee68337a6.webp"
              alt="FlareAI desktop website"
              className="w-full h-auto rounded-2xl shadow-2xl"
              sizes="80vw"
            />
            
            {/* Mobile Phone Overlay - positioned to partially overlay */}
            <div className="absolute top-1/2 right-0 translate-x-1/2 -translate-y-1/2 w-64">
              <div className="relative">
                {/* Phone Frame */}
                <div className="bg-black rounded-[2.5rem] p-2 shadow-2xl aspect-[9/19.5]">
                  <div className="bg-white rounded-[2rem] overflow-hidden h-full">
                    <OptimizedImage 
                      src="/lovable-uploads/f825daa8-03cb-41b0-a64f-28a015ef5f93.webp"
                      alt="FlareAI mobile app"
                      className="w-full h-full object-cover object-top"
                      sizes="256px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Website Description Section */}
      <div className="py-16">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">THE WEBSITE</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              The FlareAI website was designed with a dual audience in mind: small businesses looking for growth and enterprise institutions—particularly banks—focused on compliance, control, and risk management. To serve both, the homepage introduces a clear fork in the journey, allowing each segment to quickly self-identify and dive into the features that matter most to them.
            </p>
            <p>
              Understanding that modern users rarely read walls of text, we emphasized visual clarity and speed of comprehension. A 60-second explainer video is placed front and center—designed to do the heavy lifting by telling the product story in a way that's digestible, human, and engaging.
            </p>
            <p>
              The overall interface balances sleek aesthetics with conversion-focused UX, always anchoring back to Flare's core value: powerful AI agents that help businesses scale—safely.
            </p>
          </div>
        </div>
      </div>

      {/* Platform Features Section */}
      <div className="py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/8cfd677c-8cea-40f8-964e-5dcc51b3f7ce.webp"
                alt="FlareAI AI agents interface"
                className="w-full h-auto"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
            </div>
            <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/8f6f5311-21a4-4548-8217-d390bf1d1d90.webp"
                alt="FlareAI Beacon agent chat interface"
                className="w-full h-auto"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
            </div>
            <div className="rounded-2xl overflow-hidden shadow-xl border border-gray-200">
              <OptimizedImage 
                src="/lovable-uploads/b73b1359-3650-4700-ba45-3f92b6b1e1ea.webp"
                alt="FlareAI Knowledge Center interface"
                className="w-full h-auto"
                sizes="(max-width: 768px) 100vw, 33vw"
              />
            </div>
          </div>
        </div>
      </div>

      {/* UI/UX Platform Description */}
      <div className="py-16">
        <div className="max-w-4xl mx-auto px-6">
          <div className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-4">UI/UX FOR PLATFORM</div>
          <div className="space-y-6 text-gray-600 leading-relaxed">
            <p>
              The FlareAI interface was built to feel approachable, intelligent, and frictionless. At the core of the experience is a conversational UI, where users interact with specialized AI agents—each with a unique name, role, and personality designed to humanize the technology while maintaining focus on business outcomes.
            </p>
            <p>
              The dashboard presents all agents upfront in a clean, visually distinct grid, helping users quickly identify the right expert—whether it's Beacon for social, Blaze for sales, or Spark for marketing. Each interaction feels purpose-built, with agent-specific toolsets, message threads, and quick access to real human backup when needed.
            </p>
            <p>
              The interface balances powerful functionality with simple, clean navigation—ensuring users can toggle between chat, analytics, and integrations without cognitive overload. Brand colors and glows tie the entire experience together, giving FlareAI a modern, polished, and energetic feel that's easy to trust—and even easier to use.
            </p>
          </div>
        </div>
      </div>

      <ContactFooter />
    </div>
  );
};

export default FlareAICaseStudy;