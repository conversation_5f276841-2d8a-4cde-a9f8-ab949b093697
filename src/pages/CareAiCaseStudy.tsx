import { useEffect } from "react";
import { <PERSON>, <PERSON>, <PERSON>, ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Navigation from "@/components/Navigation";
import ContactFooter from "@/components/ContactFooter";
import ProjectOverview from "@/components/case-study/ProjectOverview";

const CareAiCaseStudy = () => {
  useEffect(() => {
    // Only scroll to top if there's no hash in the URL
    if (!window.location.hash) {
      window.scrollTo(0, 0);
    }
  }, []);

  return (
    <div className="bg-white">
      <Navigation />

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-gray-50 to-cyan-50/30 pt-24">
        <div className="max-w-7xl mx-auto px-6 py-20">
          <div className="grid lg:grid-cols-2 gap-20 items-center">
            {/* Left Column - Content */}
            <div className="relative z-10">
              {/* Logo */}
              <div className="mb-8 animate-fade-in">
                <img 
                  src="/lovable-uploads/dc117c4c-1228-4438-a15a-117bc5b5d1a0.webp"
                  alt="Care.AI Logo"
                  className="w-24 h-24 rounded-3xl shadow-xl hover-scale"
                />
              </div>

              {/* Title */}
              <h1 className="text-6xl md:text-7xl font-black text-gray-900 mb-6 leading-[0.9] tracking-tight">
                CARE.AI
              </h1>
              
              {/* Subtitle */}
              <p className="text-2xl md:text-3xl text-gray-700 font-light mb-8 leading-relaxed">
                From Early AI in Healthcare to
                <span className="block bg-gradient-to-r from-cyan-600 to-cyan-500 bg-clip-text text-transparent font-medium">
                  Strategic Acquisition by Stryker
                </span>
              </p>

              {/* Tags */}
              <div className="flex flex-wrap gap-3 mb-12">
                <span className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white rounded-full text-sm font-semibold shadow-lg hover-scale">
                  Healthcare
                </span>
                <span className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white rounded-full text-sm font-semibold shadow-lg hover-scale">
                  AI
                </span>
                <span className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-cyan-600 text-white rounded-full text-sm font-semibold shadow-lg hover-scale">
                  Acquisition
                </span>
              </div>

              {/* Info Grid */}
              <div className="grid md:grid-cols-3 gap-8">
                <div className="group">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 group-hover:text-cyan-600 transition-colors">What I Did</h3>
                  <ul className="space-y-3 text-gray-600">
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full"></div>
                      Brand Narrative
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full"></div>
                      Web Presence
                    </li>
                    <li className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full"></div>
                      Investor Materials
                    </li>
                  </ul>
                </div>

                <div className="group">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 group-hover:text-cyan-600 transition-colors">Industry</h3>
                  <p className="text-cyan-600 font-semibold mb-2 text-lg">Healthcare</p>
                  <p className="text-gray-600">AI-Powered Solutions</p>
                </div>

                <div className="group">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 group-hover:text-cyan-600 transition-colors">Outcome</h3>
                  <p className="text-3xl font-black text-gray-900 mb-2 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">Acquired</p>
                  <p className="text-gray-600">By Stryker in 2024</p>
                </div>
              </div>
            </div>

            {/* Right Column - Healthcare Image */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-teal-500/20 rounded-3xl blur-3xl transform rotate-6"></div>
              <div className="relative bg-white rounded-3xl overflow-hidden shadow-2xl border border-gray-100 hover-scale">
                <img 
                  src="/lovable-uploads/f40bd76d-588d-44b9-8204-910f423ab6d1.webp"
                  alt="Care.AI Smart Healthcare Platform"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <ProjectOverview 
        overview={{
          challenge: "How do you tell the story of a technology that most people don't fully understand—let alone trust? Care.ai was one of the first companies in the healthcare space to make AI its core product, and while the vision was clear, the category was still new and often met with skepticism. The challenge wasn't just messaging—it was creating a visual identity and brand experience that made cutting-edge AI feel reliable, professional, and human. The founder, a seasoned entrepreneur I'd worked with on multiple successful ventures, brought me in to help shape that story through strategic design and brand positioning. My job was to transform this abstract AI concept into a brand that healthcare professionals would immediately trust—using design as the bridge between complex technology and human confidence.",
          solution: "We positioned Care.AI as the compassionate intelligence that enhances human care rather than replacing it — creating a brand that felt both cutting-edge and deeply human.",
          timeline: "8 months from brand development to Series B fundraising with complete platform redesign.",
          results: "$180 Million raised with 100+ healthcare facilities adopting the AI care platform."
        }}
        quote={{
          text: "The real opportunity wasn't just explaining how the technology worked—it was showing what it made possible.",
          author: "Care.ai Founder"
        }}
      />

      {/* The Solution Section */}
      <section className="py-24 bg-muted/30 relative overflow-hidden">
        <div className="absolute inset-0 bg-dot-pattern opacity-10"></div>
        <div className="max-w-6xl mx-auto px-6 relative">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-primary/10 rounded-3xl mb-8">
              <img src="/lovable-uploads/ef2508a2-c7bc-4bc3-97b4-c48697345f7c.webp" alt="Care.ai icon" className="w-12 h-12" />
            </div>
            <h2 className="text-5xl md:text-6xl font-bold text-foreground mb-6 tracking-tight">
              The Solution: From AI Startup to Strategic Asset
            </h2>
            <div className="w-32 h-1 bg-gradient-to-r from-cyan-600 via-cyan-600 to-cyan-400 mx-auto rounded-full"></div>
          </div>

          {/* Opening narrative */}
          <div className="max-w-4xl mx-auto mb-20">
            <div className="text-center space-y-6 text-lg text-muted-foreground leading-relaxed">
              <p className="text-xl font-medium text-foreground">
                We weren't just designing a healthcare platform. We were positioning Care.ai as the foundational AI infrastructure that billion-dollar healthcare companies would need to own to compete in the next decade — showing how technology amplifies human care rather than replacing it.
              </p>
              <p>
                The key was positioning Care.ai not as a disruptor, but as the strategic asset that makes healthcare organizations indispensable — technology that makes healthcare professionals more effective and makes their organizations acquisition-worthy.
              </p>
              <p>
                This approach resonated with both clinical staff who needed to trust the system administrators who needed to see ROI, and strategic acquirers who needed to own the future of healthcare AI.
              </p>
            </div>
          </div>

          {/* Quote Section */}
          <div className="mb-20">
            <div className="relative max-w-3xl mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/20 via-teal-400/10 to-blue-500/20 rounded-3xl blur-2xl"></div>
              <div className="relative bg-card/90 backdrop-blur-sm border-l-4 border-cyan-600 rounded-2xl p-12 shadow-xl text-center">
                <blockquote className="text-3xl font-bold text-cyan-600 mb-6 leading-relaxed">
                  "The AI platform healthcare giants couldn't afford to let competitors acquire."
                </blockquote>
                <p className="text-muted-foreground">
                  While competitors focused on technical capabilities, Care.ai became the strategic necessity that positioned healthcare systems as industry leaders — with AI as the competitive advantage that makes better care and better business outcomes possible.
                </p>
              </div>
            </div>
          </div>

          {/* Strategic Foundation */}
          <div className="mb-20">
            <h3 className="text-3xl font-bold text-foreground text-center mb-12">The Strategic Foundation</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-cyan-400/20 to-teal-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-cyan-50 to-teal-50 backdrop-blur-sm border border-cyan-200/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-teal-600 rounded-xl flex items-center justify-center mb-6">
                    <Heart className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Market Leadership</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Building confidence in AI through positioning Care.ai as the industry standard that healthcare systems must adopt to remain competitive.
                  </p>
                  <p className="text-sm text-gray-600">
                    Every interface element was designed to signal enterprise-grade reliability and strategic necessity rather than cold healthcare technology. Healthcare providers could see exactly how AI recommendations were generated, building trust through transparency and positioning Care.ai as essential infrastructure.
                  </p>
                </div>
              </div>
              
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-blue-50 to-purple-50 backdrop-blur-sm border border-blue-200/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mb-6">
                    <Brain className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Strategic Advantage</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Making complex AI predictions feel like the competitive edge that separates industry leaders from followers.
                  </p>
                  <p className="text-sm text-gray-600">
                    We transformed dense data streams into intuitive dashboards that helped clinicians make faster, more informed decisions. The AI became the strategic asset that made healthcare organizations indispensable to their communities - not just a diagnostic tool, but the foundation of superior care delivery.
                  </p>
                </div>
              </div>
              
              <div className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-400/20 to-red-500/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-orange-50 to-red-50 backdrop-blur-sm border border-orange-200/50 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-6">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-4">Acquisition Value</h4>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    Designing workflows that made Care.ai feel like the platform billion-dollar companies needed to own the future of healthcare.
                  </p>
                  <p className="text-sm text-gray-600">
                    The platform fostered collaboration between doctors, nurses, and specialists by providing shared insights and coordinated care plans. AI became the connective tissue that transformed healthcare organizations into the kind of strategic assets that industry giants must acquire to maintain market leadership.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* The Impact */}
          <div className="mb-16">
            <h3 className="text-3xl font-bold text-foreground text-center mb-12">The Impact</h3>
            <div className="max-w-4xl mx-auto">
              <p className="text-lg text-muted-foreground mb-8 text-center">
                This strategic positioning created the foundation for a billion-dollar acquisition:
              </p>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="bg-card/60 backdrop-blur-sm border border-border/30 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">For strategic value:</h4>
                  <p className="text-sm text-muted-foreground">
                    Investors saw a company positioned as essential healthcare infrastructure that billion-dollar companies would need to acquire to compete in the AI-driven future, with proven adoption metrics and clinical outcomes that made Care.ai an inevitable acquisition target.
                  </p>
                </div>
                <div className="bg-card/60 backdrop-blur-sm border border-border/30 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">For market dominance:</h4>
                  <p className="text-sm text-muted-foreground">
                    Hospitals could present Care.ai as the competitive advantage that separated industry leaders from followers - technology that empowers their staff rather than threatens their expertise, making their organizations more valuable to strategic acquirers.
                  </p>
                </div>
                <div className="bg-card/60 backdrop-blur-sm border border-border/30 rounded-xl p-6">
                  <h4 className="font-semibold text-foreground mb-3">For acquisition appeal:</h4>
                  <p className="text-sm text-muted-foreground">
                    The platform demonstrated measurable improvements in early detection, treatment planning, and care coordination across partner facilities, creating the kind of proven ROI and market traction that makes companies irresistible to industry giants like Stryker.
                  </p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* The Work Section */}
      <section className="py-24 bg-background relative overflow-hidden">
        <div className="max-w-6xl mx-auto px-6 relative">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-bold text-foreground mb-8 tracking-tight">
              The Work
            </h2>
            <div className="max-w-3xl mx-auto mb-12">
              <p className="text-xl text-muted-foreground leading-relaxed mb-8">
                We created a comprehensive brand and platform experience that positioned 
                Care.ai as the strategic acquisition target that healthcare giants needed to own the future of AI-driven healthcare.
              </p>
            </div>
          </div>

          {/* Work Grid */}
          <div className="grid md:grid-cols-2 gap-12 mb-16">
            {/* Brand Strategy */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-gray-100 to-blue-100 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/a8210280-e83f-48f2-af08-a7723b7d8532.webp"
                    alt="Brand Strategy Development"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Brand Strategy</h3>
              <p className="text-muted-foreground leading-relaxed">
                Repositioning AI from technical jargon to human-centered healthcare transformation.
              </p>
            </div>

            {/* Landing Page Design */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-white rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/2cd57586-2fb1-4b55-90c7-b624e62447fa.webp"
                    alt="Landing Page Design"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Landing Page Design</h3>
              <p className="text-muted-foreground leading-relaxed">
                A clean, modern web presence that conveyed innovation with credibility.
              </p>
            </div>

            {/* Investor Presentation */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-blue-900 to-teal-900 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/5b1b72da-2e43-46bb-9d3b-49d7438abffe.webp"
                    alt="Investor Presentation Materials"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Investor Presentation</h3>
              <p className="text-muted-foreground leading-relaxed">
                Created compelling investor materials to support fundraising and the acquisition journey.
              </p>
            </div>

            {/* AI Feature Visualization */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-green-100 to-teal-100 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/2c5adf26-27b5-4ad1-9322-a8ab1920c015.webp"
                    alt="AI Feature Visualization"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">AI Feature Visualization</h3>
              <p className="text-muted-foreground leading-relaxed">
                Translating complex AI capabilities into easy-to-understand visual concepts.
              </p>
            </div>

            {/* Key Message Framework */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-gradient-to-br from-cyan-100 to-blue-100 rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/f3a507d7-ddbc-4d25-a190-9504b311469a.webp"
                    alt="Key Message Framework Development"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Key Message Framework</h3>
              <p className="text-muted-foreground leading-relaxed">
                Developing a cohesive messaging system for consistent communication across all channels.
              </p>
            </div>

            {/* Brand Identity Elements */}
            <div className="group">
              <div className="relative mb-6">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div className="relative bg-white rounded-2xl overflow-hidden shadow-xl aspect-[16/9]">
                  <img 
                    src="/lovable-uploads/91124c05-7416-4d0f-9e51-e4032558c68a.webp"
                    alt="Brand Identity Elements"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-3">Brand Identity Elements</h3>
              <p className="text-muted-foreground leading-relaxed">
                Visual language that balanced innovation with healthcare credibility.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* The Result Section */}
      <section className="py-24 bg-gradient-to-br from-gray-900 via-cyan-900 to-teal-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-dot-pattern opacity-10"></div>
        <div className="max-w-6xl mx-auto px-6 relative">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-6 py-3 bg-cyan-500 text-white rounded-full text-sm font-semibold mb-8">
              The Outcome
            </div>
            <h2 className="text-5xl md:text-6xl font-bold text-white mb-6 tracking-tight">
              Acquired by Stryker
            </h2>
            <p className="text-xl text-gray-300 leading-relaxed max-w-4xl mx-auto mb-8">
              The work supported not only multiple rounds of successful fundraising—but 
              ultimately led to Care.ai's acquisition by Stryker in August 2024.
            </p>
            <p className="text-lg text-gray-400 leading-relaxed max-w-3xl mx-auto">
              It's a case where the product was powerful on its own, but the story and polish 
              helped open doors faster.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Multiple */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-cyan-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <h3 className="text-3xl font-bold text-white mb-4">Multiple</h3>
                <p className="text-gray-300 leading-relaxed">
                  Successful funding rounds
                </p>
              </div>
            </div>

            {/* Strategic */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-cyan-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
              <div className="relative bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                <h3 className="text-3xl font-bold text-white mb-4">Strategic</h3>
                <p className="text-gray-300 leading-relaxed">
                  Acquisition by Stryker
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Custom Footer for Case Study */}
      <footer className="py-32 px-6 bg-gray-50 text-gray-900">
        <div className="max-w-6xl mx-auto text-center">
          {/* Main CTA Section */}
          <div className="mb-20">
            <h2 className="text-5xl md:text-6xl font-semibold text-gray-900 mb-8 leading-tight">
              Ready to build a
              <br />
              fundable brand?
            </h2>
            
            <Button 
              className="bg-[#474787] hover:bg-[#3d3f73] text-white px-8 py-4 text-lg rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105 flex items-center gap-2 mx-auto mb-16 w-fit"
              asChild
            >
              <a href="/cohort-application">
                APPLY NOW
                <ArrowRight className="w-5 h-5" />
              </a>
            </Button>
          </div>

          {/* Navigation Links */}
          <div className="mb-16">
            <div className="flex flex-wrap justify-center gap-8 text-gray-600">
              <a href="#home" className="hover:text-gray-900 transition-colors">Home</a>
              <a href="#about" className="hover:text-gray-900 transition-colors">About</a>
              <a href="#works" className="hover:text-gray-900 transition-colors">Works</a>
              <a href="#contact" className="hover:text-gray-900 transition-colors">Contact</a>
            </div>
          </div>
          
          <div className="pt-8 border-t border-gray-200">
            <p className="text-gray-500 text-center">
              Copyright © 2025 Garcia Interactive. All Rights Reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default CareAiCaseStudy;