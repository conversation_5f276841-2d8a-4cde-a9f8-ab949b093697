
import { Card, CardContent } from "@/components/ui/card";

interface ProjectOverviewProps {
  overview: {
    challenge: string;
    solution: string;
    timeline: string;
    results: string;
  };
  quote?: {
    text: string;
    author: string;
  };
  challengeImage?: {
    src: string;
    alt: string;
  };
}

const ProjectOverview = ({ overview, quote, challengeImage }: ProjectOverviewProps) => {
  const remainingCards = [
    {
      title: "Solution",
      content: overview.solution,
      color: "border-blue-200 bg-blue-50"
    },
    {
      title: "Timeline",
      content: overview.timeline,
      color: "border-yellow-200 bg-yellow-50"
    },
    {
      title: "Results",
      content: overview.results,
      color: "border-green-200 bg-green-50"
    }
  ];

  return (
    <>
      {/* Challenge Section - Split Screen Layout */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50/30">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid lg:grid-cols-5 gap-0">
            {/* Left Side - Content (60%) */}
            <div className="lg:col-span-3 px-8 py-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-8">
                The Challenge
              </h2>
              <div className="space-y-6 text-gray-700 leading-relaxed">
                <p>
                  {overview.challenge}
                </p>
              </div>
            </div>

            {/* Right Side - Image with Quote (40%) */}
            <div className="lg:col-span-2 relative p-8 flex items-center justify-center">
              <div className="relative max-w-md">
                <img 
                  src={challengeImage?.src || "/lovable-uploads/b24fde6b-6e06-4834-92ca-64da844b7d25.webp"} 
                  alt={challengeImage?.alt || "Challenge section image"} 
                  className="w-full h-auto rounded-lg shadow-lg"
                />
                
                {/* Overlaid Quote */}
                {quote && (
                  <div className="absolute transform" style={{ bottom: '-100px', right: '-100px' }}>
                    <div className="bg-white/95 backdrop-blur-sm rounded-lg p-6 shadow-lg max-w-xs">
                      <blockquote className="text-lg font-medium text-gray-900 italic mb-3">
                        "{quote.text}"
                      </blockquote>
                      <cite className="text-gray-600 font-semibold">— {quote.author}</cite>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

    </>
  );
};

export default ProjectOverview;
