
import { Button } from "@/components/ui/button";
import { <PERSON>u, <PERSON>, ArrowRight } from "lucide-react";
import { useState } from "react";
import { useLocation } from "react-router-dom";
import { useScrollToSection } from "@/hooks/use-scroll-to-section";

const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { scrollToSection } = useScrollToSection();

  // Define navigation contexts
  const isFoundersCohortPage = location.pathname === '/founders-cohort';
  const isFoundersCohortCaseStudy = location.pathname.startsWith('/founders-cohort/case-study/');
  const isCohortApplication = location.pathname === '/cohort-application';
  const isFoundersCohortContext = isFoundersCohortPage || isFoundersCohortCaseStudy || isCohortApplication;

  // Navigation link helpers
  const getNavLink = (anchor: string) => {
    if (isFoundersCohortPage) return `#${anchor}`;
    if (isFoundersCohortContext) return `/founders-cohort#${anchor}`;
    return `#${anchor}`;
  };

  const logoHref = isFoundersCohortContext ? "/founders-cohort" : "/";

  // Handle navigation clicks with custom scroll behavior
  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, anchor: string) => {
    e.preventDefault();

    // If we're already on the founders cohort page, just scroll to section
    if (isFoundersCohortPage) {
      // Update URL hash
      window.history.pushState(null, '', `#${anchor}`);
      // Scroll to section with centering
      scrollToSection(anchor);
      // Close mobile menu if open
      setIsMenuOpen(false);
      return;
    }

    // If we're in founders cohort context but not on main page, navigate to main page with hash
    if (isFoundersCohortContext) {
      window.location.href = `/founders-cohort#${anchor}`;
      return;
    }

    // For other pages, navigate normally
    window.location.href = `#${anchor}`;
  };

  // Button configurations
  const getActionButtons = () => {
    if (isFoundersCohortPage) {
      return (
        <Button
          className="bg-[#474787] hover:bg-[#3d3f73] text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105"
          asChild
        >
          <a href="/cohort-application">
            Apply Now
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
      );
    }

    if (isCohortApplication || isFoundersCohortCaseStudy) {
      return (
        <Button
          className="bg-[#474787] hover:bg-[#3d3f73] text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105"
          asChild
        >
          <a href="/founders-cohort">
            Back to Cohort Info
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
      );
    }

    // Main site buttons
    return (
      <>
        <Button
          className="bg-[#474787] hover:bg-[#3d3f73] text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-[#474787]/25 transition-all duration-200 hover:shadow-[#474787]/40 hover:scale-105"
          asChild
        >
          <a href="/founders-cohort">
            Founders Cohort
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
        <Button
          className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-orange-500/25 transition-all duration-200 hover:shadow-orange-500/40 hover:scale-105"
          asChild
        >
          <a href="/contact">
            Start Your Project
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
      </>
    );
  };

  // Mobile button configurations
  const getMobileActionButtons = () => {
    if (isFoundersCohortPage) {
      return (
        <Button
          className="bg-[#474787] hover:bg-[#3d3f73] text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-[#474787]/25 w-fit"
          asChild
        >
          <a href="/cohort-application">
            Apply Now
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
      );
    }

    if (isCohortApplication || isFoundersCohortCaseStudy) {
      return (
        <Button
          className="bg-[#474787] hover:bg-[#3d3f73] text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-[#474787]/25 w-fit"
          asChild
        >
          <a href="/founders-cohort">
            Back to Cohort Info
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
      );
    }

    // Main site buttons
    return (
      <>
        <Button
          className="bg-[#474787] hover:bg-[#3d3f73] text-white px-6 py-2.5 rounded-full font-semibold shadow-lg shadow-[#474787]/25 w-fit mt-4"
          asChild
        >
          <a href="/founders-cohort">
            Founders Cohort
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
        <Button
          className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2.5 rounded-full font-semibold shadow-orange-500/25 w-fit mt-4"
          asChild
        >
          <a href="/contact">
            Start Your Project
            <ArrowRight className="w-4 h-4 ml-2" />
          </a>
        </Button>
      </>
    );
  };

  return (
    <nav className="fixed top-4 left-1/2 transform -translate-x-1/2 w-full max-w-6xl px-6 z-50">
      <div className="bg-white/60 backdrop-blur-md border border-white/20 rounded-2xl shadow-lg shadow-black/5">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <a href={logoHref} className="flex items-center">
              <img
                src="/lovable-uploads/7b5c9905-d358-4ec2-8f63-8c1139080afe.png?v=2"
                alt="Mario Garcia"
                className="w-[135px] h-auto"
              />
            </a>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center gap-8">
              {isFoundersCohortContext ? (
                <>
                  <a href={getNavLink("case-studies")} onClick={(e) => handleNavClick(e, "case-studies")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Case Studies</a>
                  <a href={getNavLink("how-it-works")} onClick={(e) => handleNavClick(e, "how-it-works")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">How It Works</a>
                  <a href={getNavLink("who-its-for")} onClick={(e) => handleNavClick(e, "who-its-for")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Who It's For</a>
                  <a href={getNavLink("pricing")} onClick={(e) => handleNavClick(e, "pricing")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Pricing</a>
                  <a href={getNavLink("about")} onClick={(e) => handleNavClick(e, "about")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">About</a>
                </>
              ) : (
                <>
                  <a href="/" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Home</a>
                  <a href="#services" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Services</a>
                  <a href="/work" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Work</a>
                  <a href="/#about" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">About</a>
                  <a href="/contact" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Contact</a>
                </>
              )}
            </div>

            <div className="hidden md:flex items-center gap-3">
              {isFoundersCohortContext && (
                <Button
                  className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-full font-medium text-sm transition-all duration-200 hover:scale-105"
                  asChild
                >
                  <a href="/">
                    Back to main site
                  </a>
                </Button>
              )}
              {getActionButtons()}
            </div>
            
            {/* Mobile Menu Button */}
            <button 
              className="md:hidden text-gray-800"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
          
          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden mt-6 pt-6 border-t border-gray-200/50">
              <div className="flex flex-col space-y-4">
                {isFoundersCohortContext ? (
                  <>
                    <a href={getNavLink("case-studies")} onClick={(e) => handleNavClick(e, "case-studies")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Case Studies</a>
                    <a href={getNavLink("how-it-works")} onClick={(e) => handleNavClick(e, "how-it-works")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">How It Works</a>
                    <a href={getNavLink("who-its-for")} onClick={(e) => handleNavClick(e, "who-its-for")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Who It's For</a>
                    <a href={getNavLink("pricing")} onClick={(e) => handleNavClick(e, "pricing")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Pricing</a>
                    <a href={getNavLink("about")} onClick={(e) => handleNavClick(e, "about")} className="text-gray-800 hover:text-gray-900 font-medium transition-colors">About</a>

                    <Button
                      className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-full font-medium text-sm w-fit mt-4"
                      asChild
                    >
                      <a href="/">
                        Back to main site
                      </a>
                    </Button>

                    {getMobileActionButtons()}
                  </>
                ) : (
                  <>
                    <a href="/" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Home</a>
                    <a href="#services" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Services</a>
                    <a href="/work" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Work</a>
                    <a href="/#about" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">About</a>
                    <a href="/contact" className="text-gray-800 hover:text-gray-900 font-medium transition-colors">Contact</a>
                    {getMobileActionButtons()}
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
